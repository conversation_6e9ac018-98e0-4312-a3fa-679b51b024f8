/* Frozen Memberships Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Toggle Button */
.toggle-button {
  background-color: var(--primary);
  color: white;
  padding: 12px 24px;
  transition: all 0.3s ease;
  width: 100%;
  max-width: none;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.toggle-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.toggle-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.frozen-card, .history-card {
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.frozen-card:hover, .history-card:hover {
  transform: translateY(-5px);
}

/* Frozen Summary */
.frozen-summary {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 0.75rem 1.25rem;
  border-radius: var(--border-radius-md);
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
}

.summary-label {
  font-size: 0.85rem;
  color: var(--secondary);
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Table Styles */
.modern-table {
  min-width: 100%;
  white-space: nowrap;
}

/* Frozen Memberships Table - Responsive Column Widths */
.frozen-table {
  min-width: 900px; /* Minimum genişlik */
}

.frozen-table th:nth-child(1), /* Üye Adı */
.frozen-table td:nth-child(1) {
  min-width: 180px;
  max-width: 200px;
  white-space: normal;
}

.frozen-table th:nth-child(2), /* Telefon */
.frozen-table td:nth-child(2) {
  min-width: 120px;
  max-width: 140px;
}

.frozen-table th:nth-child(3), /* Branş */
.frozen-table td:nth-child(3) {
  min-width: 100px;
  max-width: 120px;
}

.frozen-table th:nth-child(4), /* Başlangıç */
.frozen-table td:nth-child(4) {
  min-width: 110px;
  max-width: 130px;
}

.frozen-table th:nth-child(5), /* Bitiş */
.frozen-table td:nth-child(5) {
  min-width: 110px;
  max-width: 130px;
}

.frozen-table th:nth-child(6), /* Kalan Gün */
.frozen-table td:nth-child(6) {
  min-width: 100px;
  max-width: 120px;
}

.frozen-table th:nth-child(7), /* İşlemler */
.frozen-table td:nth-child(7) {
  min-width: 120px;
  max-width: 150px;
}

/* History Table - Responsive Column Widths */
.history-table {
  min-width: 1400px; /* Daha geniş minimum genişlik */
}

.history-table th:nth-child(1), /* Üye Adı */
.history-table td:nth-child(1) {
  min-width: 160px;
  max-width: 180px;
  white-space: normal;
}

.history-table th:nth-child(2), /* Telefon */
.history-table td:nth-child(2) {
  min-width: 110px;
  max-width: 130px;
}

.history-table th:nth-child(3), /* Branş */
.history-table td:nth-child(3) {
  min-width: 90px;
  max-width: 110px;
}

.history-table th:nth-child(4), /* Başlangıç */
.history-table td:nth-child(4) {
  min-width: 100px;
  max-width: 120px;
}

.history-table th:nth-child(5), /* Bitiş */
.history-table td:nth-child(5) {
  min-width: 100px;
  max-width: 120px;
}

.history-table th:nth-child(6), /* Gerçek Bitiş */
.history-table td:nth-child(6) {
  min-width: 110px;
  max-width: 130px;
}

.history-table th:nth-child(7), /* Süre */
.history-table td:nth-child(7) {
  min-width: 80px;
  max-width: 100px;
}

.history-table th:nth-child(8), /* Kullanılan */
.history-table td:nth-child(8) {
  min-width: 90px;
  max-width: 110px;
}

.history-table th:nth-child(9), /* İşlem Türü */
.history-table td:nth-child(9) {
  min-width: 110px;
  max-width: 130px;
}

.history-table th:nth-child(10), /* İşlem Tarihi */
.history-table td:nth-child(10) {
  min-width: 120px;
  max-width: 140px;
  font-weight: 500;
}

/* Member Name with Avatar */
.member-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Remaining Days Styling */
.remaining-days {
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

/* Modern Modal */
.modern-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modern-modal.show {
  opacity: 1;
  visibility: visible;
}

.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.modern-modal-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.modern-modal.show .modern-modal-container {
  transform: translateY(0);
}

.modern-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-modal-title {
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--secondary);
  transition: color 0.2s ease;
}

.modern-modal-close:hover {
  color: var(--danger);
}

.modern-modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 130px);
}

.modern-modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1.25rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  gap: 0.75rem;
}

/* Modern Search Input */
.search-container {
  position: relative;
  width: 300px;
}

.modern-search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search-input input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  background-color: rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.modern-search-input input:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--secondary);
  font-size: 0.95rem;
  pointer-events: none;
}

/* Animations */
.slide-in-left {
  animation: slideInLeft 0.5s forwards;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.zoom-in {
  transition: transform 0.3s ease;
}

.zoom-in:hover {
  transform: scale(1.02);
}

/* Dark Mode Support */
[data-theme="dark"] .summary-item {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-search-input input {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-search-input input:focus {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-modal-container {
  background-color: var(--background-dark);
}

[data-theme="dark"] .modern-modal-header, 
[data-theme="dark"] .modern-modal-footer {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .frozen-summary {
    width: 100%;
    margin-top: 1rem;
  }
  
  .search-container {
    width: 100%;
    margin-top: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 0.5rem;
  }
  
  .action-buttons button:first-child {
    margin-top: 0;
  }
  
  .member-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
